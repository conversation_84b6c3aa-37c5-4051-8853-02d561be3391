#!/bin/bash

# Gemini CLI 2 API 部署脚本
# 用于快速启动和管理 Gemini API 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Node.js 版本
check_nodejs() {
    print_info "检查 Node.js 版本..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js (>= 18.0.0)"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    REQUIRED_VERSION="18.0.0"
    
    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
        print_success "Node.js 版本检查通过: v$NODE_VERSION"
    else
        print_error "Node.js 版本过低，需要 >= $REQUIRED_VERSION，当前版本: v$NODE_VERSION"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    print_info "检查并安装依赖..."
    if [ ! -d "node_modules" ]; then
        print_info "安装 npm 依赖..."
        npm install
        print_success "依赖安装完成"
    else
        print_success "依赖已存在"
    fi
}

# 启动 Gemini API 服务器
start_gemini_server() {
    local host=${1:-"localhost"}
    local port=${2:-"3000"}
    local api_key=${3:-"123456"}
    local log_mode=${4:-"none"}
    
    print_info "启动 Gemini API 服务器..."
    print_info "配置: Host=$host, Port=$port, API Key=$api_key, Log Mode=$log_mode"
    
    if [ "$log_mode" != "none" ]; then
        node gemini-api-server.js $host --port $port --api-key $api_key --log-prompts $log_mode
    else
        node gemini-api-server.js $host --port $port --api-key $api_key
    fi
}

# 启动 OpenAI 兼容服务器
start_openai_server() {
    local host=${1:-"localhost"}
    local port=${2:-"8000"}
    local api_key=${3:-"sk-gemini-proxy"}
    local log_mode=${4:-"none"}
    
    print_info "启动 OpenAI 兼容 API 服务器..."
    print_info "配置: Host=$host, Port=$port, API Key=$api_key, Log Mode=$log_mode"
    
    if [ "$log_mode" != "none" ]; then
        node openai-api-server.js $host --port $port --api-key $api_key --log-prompts $log_mode
    else
        node openai-api-server.js $host --port $port --api-key $api_key
    fi
}

# 测试服务器
test_server() {
    local server_type=${1:-"gemini"}
    local host=${2:-"localhost"}
    local port=${3:-"3000"}
    local api_key=${4:-"123456"}
    
    print_info "测试 $server_type 服务器..."
    
    if [ "$server_type" = "gemini" ]; then
        # 测试 Gemini API
        curl -s "http://$host:$port/v1beta/models?key=$api_key" | head -n 5
    else
        # 测试 OpenAI 兼容 API
        curl -s -H "Authorization: Bearer $api_key" "http://$host:$port/v1/models" | head -n 5
    fi
}

# 显示帮助信息
show_help() {
    echo "Gemini CLI 2 API 部署脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  check          检查环境和依赖"
    echo "  install        安装依赖"
    echo "  gemini         启动 Gemini API 服务器"
    echo "  openai         启动 OpenAI 兼容服务器"
    echo "  test-gemini    测试 Gemini API 服务器"
    echo "  test-openai    测试 OpenAI 兼容服务器"
    echo "  help           显示此帮助信息"
    echo ""
    echo "选项 (用于 gemini/openai 命令):"
    echo "  --host HOST    监听地址 (默认: localhost)"
    echo "  --port PORT    监听端口 (默认: gemini=3000, openai=8000)"
    echo "  --key KEY      API 密钥 (默认: gemini=123456, openai=sk-gemini-proxy)"
    echo "  --log MODE     日志模式 (none/console/file, 默认: none)"
    echo ""
    echo "示例:"
    echo "  $0 check                                    # 检查环境"
    echo "  $0 gemini                                   # 启动 Gemini 服务器"
    echo "  $0 openai --host 0.0.0.0 --port 8080       # 启动 OpenAI 服务器"
    echo "  $0 test-gemini                              # 测试 Gemini 服务器"
}

# 解析命令行参数
parse_args() {
    HOST=""
    PORT=""
    API_KEY=""
    LOG_MODE="none"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --host)
                HOST="$2"
                shift 2
                ;;
            --port)
                PORT="$2"
                shift 2
                ;;
            --key)
                API_KEY="$2"
                shift 2
                ;;
            --log)
                LOG_MODE="$2"
                shift 2
                ;;
            *)
                break
                ;;
        esac
    done
}

# 主函数
main() {
    case ${1:-help} in
        check)
            check_nodejs
            ;;
        install)
            check_nodejs
            install_dependencies
            ;;
        gemini)
            shift
            parse_args "$@"
            check_nodejs
            install_dependencies
            start_gemini_server "${HOST:-localhost}" "${PORT:-3000}" "${API_KEY:-123456}" "$LOG_MODE"
            ;;
        openai)
            shift
            parse_args "$@"
            check_nodejs
            install_dependencies
            start_openai_server "${HOST:-localhost}" "${PORT:-8000}" "${API_KEY:-sk-gemini-proxy}" "$LOG_MODE"
            ;;
        test-gemini)
            shift
            parse_args "$@"
            test_server "gemini" "${HOST:-localhost}" "${PORT:-3000}" "${API_KEY:-123456}"
            ;;
        test-openai)
            shift
            parse_args "$@"
            test_server "openai" "${HOST:-localhost}" "${PORT:-8000}" "${API_KEY:-sk-gemini-proxy}"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
