# 使用官方 Node.js 18 镜像作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S gemini -u 1001

# 更改文件所有权
RUN chown -R gemini:nodejs /app
USER gemini

# 暴露端口
EXPOSE 3000 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/v1beta/models?key=123456 || exit 1

# 默认启动 Gemini API 服务器
CMD ["node", "gemini-api-server.js", "0.0.0.0", "--port", "3000", "--api-key", "123456"]
