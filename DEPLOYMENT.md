# Gemini CLI 2 API 部署指南

本文档提供了多种部署 Gemini CLI 2 API 的方法，包括本地开发、生产环境和容器化部署。

## 📋 目录

- [前置要求](#前置要求)
- [快速开始](#快速开始)
- [部署方式](#部署方式)
  - [1. 本地开发部署](#1-本地开发部署)
  - [2. 生产环境部署](#2-生产环境部署)
  - [3. Docker 部署](#3-docker-部署)
  - [4. PM2 部署](#4-pm2-部署)
- [配置说明](#配置说明)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)

## 🔧 前置要求

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **Google 账号**: 用于 OAuth 认证
- **网络**: 能够访问 Google API 服务

### 可选要求（用于生产环境）

- **PM2**: 进程管理器
- **Docker**: 容器化部署
- **Nginx**: 反向代理（推荐）

## 🚀 快速开始

### 1. 克隆项目并安装依赖

```bash
# 如果是从 Git 仓库克隆
git clone <repository-url>
cd gemini-cli-2-api

# 安装依赖
npm install
```

### 2. 首次认证

启动服务器进行 Google OAuth 认证：

```bash
node gemini-api-server.js
```

按照终端提示完成 Google 认证流程。

### 3. 测试服务

```bash
# 测试 Gemini API
curl "http://localhost:3000/v1beta/models?key=123456"

# 测试 OpenAI 兼容 API
curl -H "Authorization: Bearer sk-gemini-proxy" "http://localhost:8000/v1/models"
```

## 📦 部署方式

### 1. 本地开发部署

#### 使用部署脚本（推荐）

**Windows:**
```cmd
# 检查环境
deploy.bat check

# 启动 Gemini API 服务器
deploy.bat gemini

# 启动 OpenAI 兼容服务器
deploy.bat openai --port 8080
```

**Linux/macOS:**
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 检查环境
./deploy.sh check

# 启动 Gemini API 服务器
./deploy.sh gemini

# 启动 OpenAI 兼容服务器
./deploy.sh openai --port 8080
```

#### 手动启动

```bash
# 启动 Gemini API 服务器（默认端口 3000）
node gemini-api-server.js

# 启动 OpenAI 兼容服务器（默认端口 8000）
node openai-api-server.js

# 自定义配置启动
node gemini-api-server.js 0.0.0.0 --port 3001 --api-key your-secret-key --log-prompts console
```

### 2. 生产环境部署

#### 环境准备

```bash
# 创建专用用户
sudo useradd -m -s /bin/bash gemini-api
sudo su - gemini-api

# 创建应用目录
mkdir -p /opt/gemini-api
cd /opt/gemini-api

# 部署代码
# ... 复制项目文件 ...

# 安装依赖
npm ci --only=production
```

#### 系统服务配置

创建 systemd 服务文件：

```bash
sudo nano /etc/systemd/system/gemini-api.service
```

```ini
[Unit]
Description=Gemini API Server
After=network.target

[Service]
Type=simple
User=gemini-api
WorkingDirectory=/opt/gemini-api
ExecStart=/usr/bin/node gemini-api-server.js 0.0.0.0 --port 3000 --api-key your-secret-key --log-prompts file
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable gemini-api
sudo systemctl start gemini-api
sudo systemctl status gemini-api
```

### 3. Docker 部署

#### 单容器部署

```bash
# 构建镜像
docker build -t gemini-api .

# 运行 Gemini API 服务器
docker run -d \
  --name gemini-api-server \
  -p 3000:3000 \
  -v $(pwd)/logs:/app/logs \
  -v gemini-oauth:/home/<USER>/.gemini \
  gemini-api

# 运行 OpenAI 兼容服务器
docker run -d \
  --name openai-api-server \
  -p 8000:8000 \
  -v $(pwd)/logs:/app/logs \
  -v gemini-oauth:/home/<USER>/.gemini \
  gemini-api \
  node openai-api-server.js 0.0.0.0 --port 8000 --api-key sk-gemini-proxy
```

#### Docker Compose 部署（推荐）

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 4. PM2 部署

#### 安装 PM2

```bash
npm install -g pm2
```

#### 使用 PM2 配置文件

```bash
# 启动所有服务
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs

# 重启服务
pm2 restart all

# 停止服务
pm2 stop all
```

## ⚙️ 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `development` |
| `PORT` | 服务端口 | `3000` (Gemini) / `8000` (OpenAI) |
| `API_KEY` | API 密钥 | `123456` (Gemini) / `sk-gemini-proxy` (OpenAI) |

### 命令行参数

| 参数 | 描述 | 示例 |
|------|------|------|
| `host` | 监听地址 | `0.0.0.0` |
| `--port` | 端口号 | `--port 3001` |
| `--api-key` | API 密钥 | `--api-key your-key` |
| `--log-prompts` | 日志模式 | `--log-prompts console` |
| `--oauth-creds-file` | OAuth 凭证文件路径 | `--oauth-creds-file /path/to/creds.json` |
| `--project-id` | GCP 项目 ID | `--project-id your-project-id` |

## 📊 监控和日志

### 日志配置

- **console**: 输出到控制台
- **file**: 输出到文件（带时间戳）
- **none**: 不记录日志（默认）

### 日志文件位置

- **提示词日志**: `prompts-YYYYMMDD-HHMMSS.log`
- **应用日志**: `logs/` 目录（PM2/Docker）
- **系统日志**: `/var/log/` （systemd）

### 健康检查

```bash
# Gemini API 健康检查
curl -f "http://localhost:3000/v1beta/models?key=123456"

# OpenAI API 健康检查
curl -f -H "Authorization: Bearer sk-gemini-proxy" "http://localhost:8000/v1/models"
```

## 🔧 故障排除

### 常见问题

1. **认证失败**
   - 检查 Google OAuth 凭证是否有效
   - 确认网络能够访问 Google API

2. **端口占用**
   - 使用 `netstat -tulpn | grep :3000` 检查端口
   - 修改端口配置或停止占用进程

3. **权限问题**
   - 确认用户有读写权限
   - 检查文件所有权设置

4. **内存不足**
   - 监控内存使用情况
   - 调整 PM2 内存限制

### 调试模式

```bash
# 启用详细日志
node gemini-api-server.js --log-prompts console

# 使用 PM2 调试
pm2 start ecosystem.config.js --env development
pm2 logs --lines 100
```

## 🔒 安全建议

1. **更改默认 API 密钥**
2. **使用 HTTPS**（通过 Nginx 反向代理）
3. **限制访问 IP**
4. **定期更新依赖**
5. **监控异常访问**

## 📝 维护

### 定期任务

- 检查日志文件大小
- 更新 Node.js 和依赖
- 备份 OAuth 凭证
- 监控服务状态

### 更新部署

```bash
# 拉取最新代码
git pull origin main

# 更新依赖
npm ci --only=production

# 重启服务
pm2 restart all
# 或
sudo systemctl restart gemini-api
```
