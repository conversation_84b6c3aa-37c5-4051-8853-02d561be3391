// OpenAI 兼容的测试服务器
import * as http from 'http';

const PORT = 8000;
const API_KEY = 'sk-gemini-proxy';

// 模拟的模型列表（OpenAI 格式）
const MODELS = {
    object: 'list',
    data: [
        {
            id: 'gemini-2.5-pro',
            object: 'model',
            created: Math.floor(Date.now() / 1000),
            owned_by: 'google',
            permission: [],
            root: 'gemini-2.5-pro',
            parent: null
        },
        {
            id: 'gemini-2.5-flash',
            object: 'model',
            created: Math.floor(Date.now() / 1000),
            owned_by: 'google',
            permission: [],
            root: 'gemini-2.5-flash',
            parent: null
        }
    ]
};

// 验证 API 密钥
function validateApiKey(req) {
    const authHeader = req.headers['authorization'];
    if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7) === API_KEY;
    }
    
    const urlParams = new URL(req.url, `http://localhost:${PORT}`);
    const keyFromUrl = urlParams.searchParams.get('key');
    const keyFromHeader = req.headers['x-goog-api-key'];
    
    return keyFromUrl === API_KEY || keyFromHeader === API_KEY;
}

// 获取请求体
function getRequestBody(req) {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                resolve(body ? JSON.parse(body) : {});
            } catch (error) {
                reject(error);
            }
        });
        req.on('error', reject);
    });
}

// 转换 OpenAI 消息格式到 Gemini 格式
function convertMessages(messages) {
    return messages.map(msg => {
        if (msg.role === 'system') {
            return { role: 'system', content: msg.content };
        } else if (msg.role === 'user') {
            return { role: 'user', content: msg.content };
        } else if (msg.role === 'assistant') {
            return { role: 'model', content: msg.content };
        }
        return msg;
    });
}

// 处理请求
async function handleRequest(req, res) {
    // 设置 CORS 头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-goog-api-key');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    const url = new URL(req.url, `http://localhost:${PORT}`);
    const path = url.pathname;
    
    console.log(`[${new Date().toISOString()}] ${req.method} ${path}`);
    
    // 验证 API 密钥
    if (!validateApiKey(req)) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
            error: { 
                message: 'Invalid API key', 
                type: 'invalid_request_error' 
            } 
        }));
        return;
    }
    
    try {
        // 处理模型列表请求
        if (path === '/v1/models' && req.method === 'GET') {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(MODELS));
            return;
        }
        
        // 处理聊天完成请求
        if (path === '/v1/chat/completions' && req.method === 'POST') {
            const body = await getRequestBody(req);
            const { messages, model, stream = false, max_tokens, temperature } = body;
            
            const convertedMessages = convertMessages(messages || []);
            const lastMessage = convertedMessages[convertedMessages.length - 1];
            
            // 构建响应内容
            const responseText = `[测试模式 - OpenAI 兼容] 这是一个模拟响应。\n\n模型: ${model || 'gemini-2.5-pro'}\n用户消息: ${lastMessage?.content || '无内容'}\n\n实际部署需要完成 Google OAuth 认证。`;
            
            if (stream) {
                // 流式响应
                res.writeHead(200, { 
                    'Content-Type': 'text/plain',
                    'Transfer-Encoding': 'chunked'
                });
                
                const chunks = responseText.split(' ');
                const id = 'chatcmpl-' + Math.random().toString(36).substring(2, 15);
                
                for (let i = 0; i < chunks.length; i++) {
                    setTimeout(() => {
                        const chunk = {
                            id,
                            object: 'chat.completion.chunk',
                            created: Math.floor(Date.now() / 1000),
                            model: model || 'gemini-2.5-pro',
                            choices: [{
                                index: 0,
                                delta: i === 0 ? { role: 'assistant', content: chunks[i] + ' ' } : { content: chunks[i] + ' ' },
                                finish_reason: i === chunks.length - 1 ? 'stop' : null
                            }]
                        };
                        
                        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                        
                        if (i === chunks.length - 1) {
                            res.write('data: [DONE]\n\n');
                            res.end();
                        }
                    }, i * 100);
                }
            } else {
                // 非流式响应
                const response = {
                    id: 'chatcmpl-' + Math.random().toString(36).substring(2, 15),
                    object: 'chat.completion',
                    created: Math.floor(Date.now() / 1000),
                    model: model || 'gemini-2.5-pro',
                    choices: [{
                        index: 0,
                        message: {
                            role: 'assistant',
                            content: responseText
                        },
                        finish_reason: 'stop'
                    }],
                    usage: {
                        prompt_tokens: 20,
                        completion_tokens: 50,
                        total_tokens: 70
                    }
                };
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(response));
            }
            return;
        }
        
        // 404 处理
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
            error: { 
                message: 'Not found', 
                type: 'invalid_request_error' 
            } 
        }));
        
    } catch (error) {
        console.error('Error handling request:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
            error: { 
                message: 'Internal server error', 
                type: 'server_error' 
            } 
        }));
    }
}

// 创建服务器
const server = http.createServer(handleRequest);

server.listen(PORT, 'localhost', () => {
    console.log('🚀 OpenAI 兼容测试服务器启动成功!');
    console.log(`📍 地址: http://localhost:${PORT}`);
    console.log(`🔑 API Key: ${API_KEY}`);
    console.log('');
    console.log('⚠️  注意: 这是一个测试服务器，提供模拟响应');
    console.log('   实际部署需要完成 Google OAuth 认证');
    console.log('');
    console.log('🧪 测试命令:');
    console.log(`   curl -H "Authorization: Bearer ${API_KEY}" "http://localhost:${PORT}/v1/models"`);
    console.log(`   curl -X POST "http://localhost:${PORT}/v1/chat/completions" \\`);
    console.log(`     -H "Content-Type: application/json" \\`);
    console.log(`     -H "Authorization: Bearer ${API_KEY}" \\`);
    console.log(`     -d '{"model":"gemini-2.5-pro","messages":[{"role":"user","content":"Hello!"}]}'`);
});

server.on('error', (error) => {
    console.error('服务器启动失败:', error);
    process.exit(1);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});
