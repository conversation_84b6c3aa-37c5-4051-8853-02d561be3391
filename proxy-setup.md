# 代理设置指南

如果您在无法直接访问 Google 服务的环境中使用此项目，需要设置代理。

## Windows 代理设置

### 方法 1: 命令行设置（临时）

```cmd
# CMD
set http_proxy=http://127.0.0.1:7890
set https_proxy=http://127.0.0.1:7890
node gemini-api-server.js

# PowerShell
$env:http_proxy="http://127.0.0.1:7890"
$env:https_proxy="http://127.0.0.1:7890"
node gemini-api-server.js
```

### 方法 2: 创建启动脚本

创建 `start-with-proxy.bat`:

```batch
@echo off
set http_proxy=http://127.0.0.1:7890
set https_proxy=http://127.0.0.1:7890
echo 代理已设置: %http_proxy%
node gemini-api-server.js %*
```

使用方法：
```cmd
start-with-proxy.bat --log-prompts console
```

## Linux/macOS 代理设置

### 方法 1: 命令行设置（临时）

```bash
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890
node gemini-api-server.js
```

### 方法 2: 创建启动脚本

创建 `start-with-proxy.sh`:

```bash
#!/bin/bash
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890
echo "代理已设置: $http_proxy"
node gemini-api-server.js "$@"
```

使用方法：
```bash
chmod +x start-with-proxy.sh
./start-with-proxy.sh --log-prompts console
```

## 常用代理端口

根据您使用的代理软件，端口可能不同：

- **Clash**: 7890 (HTTP), 7891 (SOCKS5)
- **V2Ray**: 10809 (HTTP), 10808 (SOCKS5)
- **Shadowsocks**: 1080 (SOCKS5)
- **Proxifier**: 8080 (HTTP)

## 验证代理设置

```bash
# 测试代理连接
curl -x http://127.0.0.1:7890 https://www.google.com

# 或使用环境变量
export https_proxy=http://127.0.0.1:7890
curl https://www.google.com
```

## Docker 代理设置

如果使用 Docker 部署，需要在容器中设置代理：

```yaml
# docker-compose.yml
version: '3.8'
services:
  gemini-api:
    build: .
    environment:
      - http_proxy=http://host.docker.internal:7890
      - https_proxy=http://host.docker.internal:7890
    # ... 其他配置
```

## 故障排除

1. **确认代理软件正在运行**
2. **检查代理端口是否正确**
3. **测试代理连接是否正常**
4. **确认防火墙没有阻止连接**

## 离线认证方案

如果代理设置仍然有问题，可以考虑：

1. **在有网络的环境中完成认证**
2. **复制生成的 `oauth_creds.json` 文件**
3. **在目标环境中使用 `--oauth-creds-file` 参数**

```bash
node gemini-api-server.js --oauth-creds-file /path/to/oauth_creds.json
```
