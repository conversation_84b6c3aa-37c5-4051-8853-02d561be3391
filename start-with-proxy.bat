@echo off
setlocal

REM Gemini API 代理启动脚本
REM 用于在需要代理的环境中启动服务

echo ========================================
echo Gemini API 代理启动脚本
echo ========================================

REM 默认代理设置（请根据您的代理软件调整）
set DEFAULT_PROXY=http://127.0.0.1:7890

REM 检查是否提供了自定义代理
if "%1"=="--proxy" (
    set PROXY_URL=%2
    shift
    shift
) else (
    set PROXY_URL=%DEFAULT_PROXY%
)

REM 设置代理环境变量
set http_proxy=%PROXY_URL%
set https_proxy=%PROXY_URL%

echo [INFO] 代理设置: %PROXY_URL%

REM 测试代理连接
echo [INFO] 测试代理连接...
curl -s --connect-timeout 5 -x %PROXY_URL% https://www.google.com >nul 2>&1
if errorlevel 1 (
    echo [WARNING] 代理连接测试失败，请检查代理设置
    echo [WARNING] 如果您不需要代理，请直接使用: node gemini-api-server.js
    pause
) else (
    echo [SUCCESS] 代理连接正常
)

echo.
echo [INFO] 启动 Gemini API 服务器...
echo [INFO] 如需停止服务，请按 Ctrl+C

REM 启动服务器，传递所有剩余参数
node gemini-api-server.js %*

echo.
echo [INFO] 服务器已停止
pause
