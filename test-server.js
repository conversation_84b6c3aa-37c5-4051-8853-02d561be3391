// 简单的测试服务器，用于验证基本功能
import * as http from 'http';

const PORT = 3000;
const API_KEY = '123456';

// 模拟的模型列表
const MODELS = {
    models: [
        {
            name: 'models/gemini-2.5-pro',
            displayName: 'Gemini 2.5 Pro',
            description: 'High-performance model for complex tasks'
        },
        {
            name: 'models/gemini-2.5-flash',
            displayName: 'Gemini 2.5 Flash',
            description: 'Fast model for quick responses'
        }
    ]
};

// 验证 API 密钥
function validateApiKey(req) {
    const urlParams = new URL(req.url, `http://localhost:${PORT}`);
    const keyFromUrl = urlParams.searchParams.get('key');
    const keyFromHeader = req.headers['x-goog-api-key'] || req.headers['authorization']?.replace('Bearer ', '');
    
    return keyFromUrl === API_KEY || keyFromHeader === API_KEY;
}

// 获取请求体
function getRequestBody(req) {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                resolve(body ? JSON.parse(body) : {});
            } catch (error) {
                reject(error);
            }
        });
        req.on('error', reject);
    });
}

// 处理请求
async function handleRequest(req, res) {
    // 设置 CORS 头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-goog-api-key, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    const url = new URL(req.url, `http://localhost:${PORT}`);
    const path = url.pathname;
    
    console.log(`[${new Date().toISOString()}] ${req.method} ${path}`);
    
    // 验证 API 密钥
    if (!validateApiKey(req)) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid API key' }));
        return;
    }
    
    try {
        // 处理模型列表请求
        if (path === '/v1beta/models' && req.method === 'GET') {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(MODELS));
            return;
        }
        
        // 处理生成内容请求
        if (path.includes('generateContent') && req.method === 'POST') {
            const body = await getRequestBody(req);
            
            // 模拟响应
            const mockResponse = {
                candidates: [{
                    content: {
                        parts: [{
                            text: `[测试模式] 这是一个模拟响应。实际部署需要完成 Google OAuth 认证。\n\n您的请求内容: ${JSON.stringify(body.contents?.[0]?.parts?.[0]?.text || '无内容', null, 2)}`
                        }],
                        role: 'model'
                    },
                    finishReason: 'STOP'
                }],
                usageMetadata: {
                    promptTokenCount: 10,
                    candidatesTokenCount: 50,
                    totalTokenCount: 60
                }
            };
            
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(mockResponse));
            return;
        }
        
        // 处理流式生成请求
        if (path.includes('streamGenerateContent') && req.method === 'POST') {
            const body = await getRequestBody(req);
            
            res.writeHead(200, { 
                'Content-Type': 'application/json',
                'Transfer-Encoding': 'chunked'
            });
            
            // 模拟流式响应
            const chunks = [
                { candidates: [{ content: { parts: [{ text: '[测试模式] ' }], role: 'model' } }] },
                { candidates: [{ content: { parts: [{ text: '这是一个模拟的流式响应。' }], role: 'model' } }] },
                { candidates: [{ content: { parts: [{ text: '\n\n实际部署需要完成 Google OAuth 认证。' }], role: 'model' } }] },
                { candidates: [{ content: { parts: [{ text: `\n\n您的请求: ${body.contents?.[0]?.parts?.[0]?.text || '无内容'}` }], role: 'model' }, finishReason: 'STOP' }] }
            ];
            
            for (let i = 0; i < chunks.length; i++) {
                setTimeout(() => {
                    res.write(JSON.stringify(chunks[i]) + '\n');
                    if (i === chunks.length - 1) {
                        res.end();
                    }
                }, i * 500);
            }
            return;
        }
        
        // 404 处理
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not found' }));
        
    } catch (error) {
        console.error('Error handling request:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Internal server error' }));
    }
}

// 创建服务器
const server = http.createServer(handleRequest);

server.listen(PORT, 'localhost', () => {
    console.log('🚀 测试服务器启动成功!');
    console.log(`📍 地址: http://localhost:${PORT}`);
    console.log(`🔑 API Key: ${API_KEY}`);
    console.log('');
    console.log('⚠️  注意: 这是一个测试服务器，提供模拟响应');
    console.log('   实际部署需要完成 Google OAuth 认证');
    console.log('');
    console.log('🧪 测试命令:');
    console.log(`   curl "http://localhost:${PORT}/v1beta/models?key=${API_KEY}"`);
    console.log(`   curl -X POST "http://localhost:${PORT}/v1beta/models/gemini-2.5-pro:generateContent" \\`);
    console.log(`     -H "Content-Type: application/json" \\`);
    console.log(`     -H "x-goog-api-key: ${API_KEY}" \\`);
    console.log(`     -d '{"contents":[{"parts":[{"text":"Hello, world!"}]}]}'`);
});

server.on('error', (error) => {
    console.error('服务器启动失败:', error);
    process.exit(1);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});
