module.exports = {
  apps: [
    {
      name: 'gemini-api-server',
      script: 'gemini-api-server.js',
      args: '0.0.0.0 --port 3000 --api-key 123456 --log-prompts file',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/gemini-api-error.log',
      out_file: './logs/gemini-api-out.log',
      log_file: './logs/gemini-api-combined.log',
      time: true
    },
    {
      name: 'openai-api-server',
      script: 'openai-api-server.js',
      args: '0.0.0.0 --port 8000 --api-key sk-gemini-proxy --log-prompts file',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 8000
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 8000
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/openai-api-error.log',
      out_file: './logs/openai-api-out.log',
      log_file: './logs/openai-api-combined.log',
      time: true
    }
  ],

  deploy: {
    production: {
      user: 'node',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/gemini-cli-2-api.git',
      path: '/var/www/gemini-cli-2-api',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
