@echo off
setlocal enabledelayedexpansion

REM Gemini CLI 2 API 部署脚本 (Windows 版本)
REM 用于快速启动和管理 Gemini API 服务

title Gemini CLI 2 API 部署工具

:main
if "%1"=="" goto show_help
if "%1"=="help" goto show_help
if "%1"=="--help" goto show_help
if "%1"=="-h" goto show_help
if "%1"=="check" goto check_env
if "%1"=="install" goto install_deps
if "%1"=="gemini" goto start_gemini
if "%1"=="openai" goto start_openai
if "%1"=="test-gemini" goto test_gemini
if "%1"=="test-openai" goto test_openai

echo [ERROR] 未知命令: %1
goto show_help

:check_env
echo [INFO] 检查 Node.js 版本...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装，请先安装 Node.js ^(^>= 18.0.0^)
    exit /b 1
)

for /f "tokens=1" %%i in ('node --version') do set NODE_VERSION=%%i
echo [SUCCESS] Node.js 版本检查通过: %NODE_VERSION%
goto :eof

:install_deps
call :check_env
if errorlevel 1 exit /b 1

echo [INFO] 检查并安装依赖...
if not exist "node_modules" (
    echo [INFO] 安装 npm 依赖...
    npm install
    if errorlevel 1 (
        echo [ERROR] 依赖安装失败
        exit /b 1
    )
    echo [SUCCESS] 依赖安装完成
) else (
    echo [SUCCESS] 依赖已存在
)
goto :eof

:start_gemini
call :install_deps
if errorlevel 1 exit /b 1

set HOST=localhost
set PORT=3000
set API_KEY=123456
set LOG_MODE=none

REM 解析参数
:parse_gemini_args
if "%2"=="" goto run_gemini
if "%2"=="--host" (
    set HOST=%3
    shift
    shift
    goto parse_gemini_args
)
if "%2"=="--port" (
    set PORT=%3
    shift
    shift
    goto parse_gemini_args
)
if "%2"=="--key" (
    set API_KEY=%3
    shift
    shift
    goto parse_gemini_args
)
if "%2"=="--log" (
    set LOG_MODE=%3
    shift
    shift
    goto parse_gemini_args
)
shift
goto parse_gemini_args

:run_gemini
echo [INFO] 启动 Gemini API 服务器...
echo [INFO] 配置: Host=%HOST%, Port=%PORT%, API Key=%API_KEY%, Log Mode=%LOG_MODE%

if "%LOG_MODE%"=="none" (
    node gemini-api-server.js %HOST% --port %PORT% --api-key %API_KEY%
) else (
    node gemini-api-server.js %HOST% --port %PORT% --api-key %API_KEY% --log-prompts %LOG_MODE%
)
goto :eof

:start_openai
call :install_deps
if errorlevel 1 exit /b 1

set HOST=localhost
set PORT=8000
set API_KEY=sk-gemini-proxy
set LOG_MODE=none

REM 解析参数
:parse_openai_args
if "%2"=="" goto run_openai
if "%2"=="--host" (
    set HOST=%3
    shift
    shift
    goto parse_openai_args
)
if "%2"=="--port" (
    set PORT=%3
    shift
    shift
    goto parse_openai_args
)
if "%2"=="--key" (
    set API_KEY=%3
    shift
    shift
    goto parse_openai_args
)
if "%2"=="--log" (
    set LOG_MODE=%3
    shift
    shift
    goto parse_openai_args
)
shift
goto parse_openai_args

:run_openai
echo [INFO] 启动 OpenAI 兼容 API 服务器...
echo [INFO] 配置: Host=%HOST%, Port=%PORT%, API Key=%API_KEY%, Log Mode=%LOG_MODE%

if "%LOG_MODE%"=="none" (
    node openai-api-server.js %HOST% --port %PORT% --api-key %API_KEY%
) else (
    node openai-api-server.js %HOST% --port %PORT% --api-key %API_KEY% --log-prompts %LOG_MODE%
)
goto :eof

:test_gemini
echo [INFO] 测试 Gemini API 服务器...
curl -s "http://localhost:3000/v1beta/models?key=123456"
goto :eof

:test_openai
echo [INFO] 测试 OpenAI 兼容 API 服务器...
curl -s -H "Authorization: Bearer sk-gemini-proxy" "http://localhost:8000/v1/models"
goto :eof

:show_help
echo.
echo Gemini CLI 2 API 部署脚本 ^(Windows 版本^)
echo.
echo 用法: %0 [命令] [选项]
echo.
echo 命令:
echo   check          检查环境和依赖
echo   install        安装依赖
echo   gemini         启动 Gemini API 服务器
echo   openai         启动 OpenAI 兼容服务器
echo   test-gemini    测试 Gemini API 服务器
echo   test-openai    测试 OpenAI 兼容服务器
echo   help           显示此帮助信息
echo.
echo 选项 ^(用于 gemini/openai 命令^):
echo   --host HOST    监听地址 ^(默认: localhost^)
echo   --port PORT    监听端口 ^(默认: gemini=3000, openai=8000^)
echo   --key KEY      API 密钥 ^(默认: gemini=123456, openai=sk-gemini-proxy^)
echo   --log MODE     日志模式 ^(none/console/file, 默认: none^)
echo.
echo 示例:
echo   %0 check                                    # 检查环境
echo   %0 gemini                                   # 启动 Gemini 服务器
echo   %0 openai --host 0.0.0.0 --port 8080       # 启动 OpenAI 服务器
echo   %0 test-gemini                              # 测试 Gemini 服务器
echo.
goto :eof
