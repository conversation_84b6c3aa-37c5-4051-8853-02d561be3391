version: '3.8'

services:
  gemini-api:
    build: .
    container_name: gemini-api-server
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - ./logs:/app/logs
      - gemini-oauth:/home/<USER>/.gemini
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/v1beta/models?key=123456"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: ["node", "gemini-api-server.js", "0.0.0.0", "--port", "3000", "--api-key", "123456", "--log-prompts", "file"]

  openai-api:
    build: .
    container_name: openai-api-server
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
    volumes:
      - ./logs:/app/logs
      - gemini-oauth:/home/<USER>/.gemini
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/v1/models", "-H", "Authorization: Bearer sk-gemini-proxy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: ["node", "openai-api-server.js", "0.0.0.0", "--port", "8000", "--api-key", "sk-gemini-proxy", "--log-prompts", "file"]
    depends_on:
      - gemini-api

volumes:
  gemini-oauth:
    driver: local
