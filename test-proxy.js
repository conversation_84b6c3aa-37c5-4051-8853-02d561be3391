// 测试代理连接的脚本
import https from 'https';
import http from 'http';
import { URL } from 'url';

// 常用代理端口列表
const commonPorts = [7890, 7891, 10809, 10808, 1080, 8080, 8888, 1087];

async function testProxy(port) {
    return new Promise((resolve) => {
        const proxyUrl = `http://127.0.0.1:${port}`;
        
        // 设置代理环境变量
        process.env.http_proxy = proxyUrl;
        process.env.https_proxy = proxyUrl;
        
        const options = {
            hostname: 'www.google.com',
            port: 443,
            path: '/',
            method: 'HEAD',
            timeout: 5000
        };
        
        const req = https.request(options, (res) => {
            resolve({ port, success: true, status: res.statusCode });
        });
        
        req.on('error', (err) => {
            resolve({ port, success: false, error: err.message });
        });
        
        req.on('timeout', () => {
            req.destroy();
            resolve({ port, success: false, error: 'timeout' });
        });
        
        req.end();
    });
}

async function findWorkingProxy() {
    console.log('🔍 正在测试常用代理端口...\n');
    
    for (const port of commonPorts) {
        console.log(`测试端口 ${port}...`);
        const result = await testProxy(port);
        
        if (result.success) {
            console.log(`✅ 端口 ${port} 可用! (状态码: ${result.status})`);
            console.log(`\n🎉 找到可用代理: http://127.0.0.1:${port}`);
            console.log(`\n请使用以下命令启动服务器:`);
            console.log(`export http_proxy=http://127.0.0.1:${port}`);
            console.log(`export https_proxy=http://127.0.0.1:${port}`);
            console.log(`node gemini-api-server.js`);
            return port;
        } else {
            console.log(`❌ 端口 ${port} 不可用 (${result.error})`);
        }
    }
    
    console.log('\n❌ 未找到可用的代理端口');
    console.log('请检查您的代理软件是否正在运行，或手动指定代理端口');
    return null;
}

// 如果直接运行此脚本
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

if (process.argv[1] === __filename) {
    findWorkingProxy().then(port => {
        process.exit(port ? 0 : 1);
    });
}

export { testProxy, findWorkingProxy };
